use serde::{Deserialize, Serialize};
use std::sync::{Arc, Mutex};
use uuid::Uuid;

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct ClipboardEntry {
    pub id: String,
    pub content: String,
    pub r#type: String, // "text", "image", "file"
    pub timestamp: u64,
    pub is_favorite: bool,
    pub preview: Option<String>,
    pub metadata: Option<ClipboardMetadata>,
}

#[derive(Debu<PERSON>, <PERSON>lone, Serialize, Deserialize)]
pub struct ClipboardMetadata {
    pub source: Option<String>,
    pub size: Option<u64>,
    pub format: Option<String>,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct KeyboardShortcuts {
    pub toggle_app: String,
    pub clear_history: String,
    pub toggle_favorite: String,
    pub copy_selected: String,
    pub delete_selected: String,
    pub search: String,
    pub navigate_up: String,
    pub navigate_down: String,
}

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct Preferences {
    pub theme: String,
    pub shortcuts: KeyboardShortcuts,
    pub auto_start: bool,
    pub show_in_system_tray: bool,
    pub max_history_length: usize,
    pub enable_notifications: bool,
}

impl Default for Preferences {
    fn default() -> Self {
        Self {
            theme: "dark".to_string(),
            shortcuts: KeyboardShortcuts {
                toggle_app: "CommandOrControl+Shift+V".to_string(),
                clear_history: "CommandOrControl+Shift+Delete".to_string(),
                toggle_favorite: "CommandOrControl+D".to_string(),
                copy_selected: "Enter".to_string(),
                delete_selected: "Delete".to_string(),
                search: "CommandOrControl+F".to_string(),
                navigate_up: "ArrowUp".to_string(),
                navigate_down: "ArrowDown".to_string(),
            },
            auto_start: false,
            show_in_system_tray: true,
            max_history_length: 100,
            enable_notifications: true,
        }
    }
}

pub struct ClipboardManager {
    pub history: Arc<Mutex<Vec<ClipboardEntry>>>,
    pub preferences: Arc<Mutex<Preferences>>,
    pub is_monitoring: Arc<Mutex<bool>>,
}

impl ClipboardManager {
    pub fn new() -> Self {
        Self {
            history: Arc::new(Mutex::new(Vec::new())),
            preferences: Arc::new(Mutex::new(Preferences::default())),
            is_monitoring: Arc::new(Mutex::new(false)),
        }
    }

    pub fn add_entry(&self, content: String, entry_type: String) -> Result<ClipboardEntry, String> {
        let mut history = self.history.lock().map_err(|e| e.to_string())?;
        let preferences = self.preferences.lock().map_err(|e| e.to_string())?;

        // Check if content already exists at the top of history
        if let Some(latest) = history.first() {
            if latest.content == content {
                return Ok(latest.clone());
            }
        }

        let entry = ClipboardEntry {
            id: Uuid::new_v4().to_string(),
            content,
            r#type: entry_type,
            timestamp: std::time::SystemTime::now()
                .duration_since(std::time::UNIX_EPOCH)
                .unwrap()
                .as_millis() as u64,
            is_favorite: false,
            preview: None,
            metadata: None,
        };

        // Add to beginning of history
        history.insert(0, entry.clone());

        // Trim history if it exceeds max length
        if history.len() > preferences.max_history_length {
            history.truncate(preferences.max_history_length);
        }

        Ok(entry)
    }

    pub fn get_history(&self) -> Result<Vec<ClipboardEntry>, String> {
        let history = self.history.lock().map_err(|e| e.to_string())?;
        Ok(history.clone())
    }

    pub fn toggle_favorite(&self, entry_id: &str) -> Result<ClipboardEntry, String> {
        let mut history = self.history.lock().map_err(|e| e.to_string())?;
        
        if let Some(entry) = history.iter_mut().find(|e| e.id == entry_id) {
            entry.is_favorite = !entry.is_favorite;
            Ok(entry.clone())
        } else {
            Err("Entry not found".to_string())
        }
    }

    pub fn delete_entry(&self, entry_id: &str) -> Result<(), String> {
        let mut history = self.history.lock().map_err(|e| e.to_string())?;
        history.retain(|entry| entry.id != entry_id);
        Ok(())
    }

    pub fn clear_history(&self) -> Result<(), String> {
        let mut history = self.history.lock().map_err(|e| e.to_string())?;
        history.clear();
        Ok(())
    }

    pub fn get_preferences(&self) -> Result<Preferences, String> {
        let preferences = self.preferences.lock().map_err(|e| e.to_string())?;
        Ok(preferences.clone())
    }

    pub fn update_preferences(&self, new_preferences: Preferences) -> Result<Preferences, String> {
        let mut preferences = self.preferences.lock().map_err(|e| e.to_string())?;
        *preferences = new_preferences.clone();
        Ok(new_preferences)
    }

    pub fn set_monitoring(&self, monitoring: bool) -> Result<(), String> {
        let mut is_monitoring = self.is_monitoring.lock().map_err(|e| e.to_string())?;
        *is_monitoring = monitoring;
        Ok(())
    }

    pub fn is_monitoring(&self) -> Result<bool, String> {
        let is_monitoring = self.is_monitoring.lock().map_err(|e| e.to_string())?;
        Ok(*is_monitoring)
    }
}
