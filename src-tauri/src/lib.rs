mod clipboard;

use clipboard::{ClipboardManager, ClipboardEntry, Preferences};
use std::sync::Arc;
use tauri::State;

// Application state
pub struct AppState {
    clipboard_manager: Arc<ClipboardManager>,
}

// Tauri commands
#[tauri::command]
async fn get_clipboard_history(state: State<'_, AppState>) -> Result<Vec<ClipboardEntry>, String> {
    state.clipboard_manager.get_history()
}

#[tauri::command]
async fn save_clipboard_entry(
    content: String,
    entry_type: String,
    state: State<'_, AppState>,
) -> Result<ClipboardEntry, String> {
    state.clipboard_manager.add_entry(content, entry_type)
}

#[tauri::command]
async fn toggle_favorite(
    entry_id: String,
    state: State<'_, AppState>,
) -> Result<ClipboardEntry, String> {
    state.clipboard_manager.toggle_favorite(&entry_id)
}

#[tauri::command]
async fn delete_clipboard_entry(
    entry_id: String,
    state: State<'_, AppState>,
) -> Result<(), String> {
    state.clipboard_manager.delete_entry(&entry_id)
}

#[tauri::command]
async fn clear_clipboard_history(state: State<'_, AppState>) -> Result<(), String> {
    state.clipboard_manager.clear_history()
}

#[tauri::command]
async fn get_preferences(state: State<'_, AppState>) -> Result<Preferences, String> {
    state.clipboard_manager.get_preferences()
}

#[tauri::command]
async fn save_preferences(
    preferences: Preferences,
    state: State<'_, AppState>,
) -> Result<Preferences, String> {
    state.clipboard_manager.update_preferences(preferences)
}

#[tauri::command]
async fn start_clipboard_monitoring(state: State<'_, AppState>) -> Result<(), String> {
    state.clipboard_manager.set_monitoring(true)
}

#[tauri::command]
async fn stop_clipboard_monitoring(state: State<'_, AppState>) -> Result<(), String> {
    state.clipboard_manager.set_monitoring(false)
}

#[tauri::command]
async fn update_global_shortcuts() -> Result<(), String> {
    // TODO: Implement global shortcut updates
    Ok(())
}

#[cfg_attr(mobile, tauri::mobile_entry_point)]
pub fn run() {
    let clipboard_manager = Arc::new(ClipboardManager::new());

    tauri::Builder::default()
        .plugin(tauri_plugin_opener::init())
        .plugin(tauri_plugin_clipboard_manager::init())
        .plugin(tauri_plugin_global_shortcut::Builder::new().build())
        .plugin(tauri_plugin_store::Builder::default().build())
        .manage(AppState {
            clipboard_manager: clipboard_manager.clone(),
        })
        .invoke_handler(tauri::generate_handler![
            get_clipboard_history,
            save_clipboard_entry,
            toggle_favorite,
            delete_clipboard_entry,
            clear_clipboard_history,
            get_preferences,
            save_preferences,
            start_clipboard_monitoring,
            stop_clipboard_monitoring,
            update_global_shortcuts,
        ])
        .setup(move |app| {
            // Initialize clipboard monitoring
            let _app_handle = app.handle().clone();
            let _clipboard_manager_clone = clipboard_manager.clone();

            tauri::async_runtime::spawn(async move {
                // TODO: Set up clipboard monitoring loop
                // This would involve listening to clipboard changes
                // and calling clipboard_manager.add_entry() when changes occur
            });

            Ok(())
        })
        .run(tauri::generate_context!())
        .expect("error while running tauri application");
}
