mod clipboard;
mod clipboard_monitor;

use clipboard::{ClipboardManager, ClipboardEntry, Preferences};
use clipboard_monitor::ClipboardMonitor;
use std::sync::Arc;
use tauri::State;

// Application state
pub struct AppState {
    clipboard_manager: Arc<ClipboardManager>,
    clipboard_monitor: Arc<ClipboardMonitor>,
}

// <PERSON><PERSON> commands
#[tauri::command]
async fn get_clipboard_history(state: State<'_, AppState>) -> Result<Vec<ClipboardEntry>, String> {
    state.clipboard_manager.get_history()
}

#[tauri::command]
async fn save_clipboard_entry(
    content: String,
    entry_type: String,
    state: State<'_, AppState>,
) -> Result<ClipboardEntry, String> {
    state.clipboard_manager.add_entry(content, entry_type)
}

#[tauri::command]
async fn toggle_favorite(
    entry_id: String,
    state: State<'_, AppState>,
) -> Result<ClipboardEntry, String> {
    state.clipboard_manager.toggle_favorite(&entry_id)
}

#[tauri::command]
async fn delete_clipboard_entry(
    entry_id: String,
    state: State<'_, AppState>,
) -> Result<(), String> {
    state.clipboard_manager.delete_entry(&entry_id)
}

#[tauri::command]
async fn clear_clipboard_history(state: State<'_, AppState>) -> Result<(), String> {
    state.clipboard_manager.clear_history()
}

#[tauri::command]
async fn get_preferences(state: State<'_, AppState>) -> Result<Preferences, String> {
    state.clipboard_manager.get_preferences()
}

#[tauri::command]
async fn save_preferences(
    preferences: Preferences,
    state: State<'_, AppState>,
) -> Result<Preferences, String> {
    state.clipboard_manager.update_preferences(preferences)
}

#[tauri::command]
async fn start_clipboard_monitoring(state: State<'_, AppState>) -> Result<(), String> {
    state.clipboard_monitor.start_monitoring().await.map_err(|e| e.to_string())
}

#[tauri::command]
async fn stop_clipboard_monitoring(state: State<'_, AppState>) -> Result<(), String> {
    state.clipboard_monitor.stop_monitoring().map_err(|e| e.to_string())
}

#[tauri::command]
async fn is_monitoring_clipboard(state: State<'_, AppState>) -> Result<bool, String> {
    Ok(state.clipboard_monitor.is_running())
}

#[tauri::command]
async fn update_global_shortcuts() -> Result<(), String> {
    // TODO: Implement global shortcut updates
    Ok(())
}

#[tauri::command]
async fn get_clipboard_status(state: State<'_, AppState>) -> Result<String, String> {
    let is_monitoring = state.clipboard_monitor.is_running();
    let history = state.clipboard_manager.get_history()?;
    Ok(format!("Monitoring: {}, History entries: {}", is_monitoring, history.len()))
}

#[cfg_attr(mobile, tauri::mobile_entry_point)]
pub fn run() {
    let clipboard_manager = Arc::new(ClipboardManager::new());
    let clipboard_monitor = Arc::new(
        ClipboardMonitor::new(clipboard_manager.clone())
            .expect("Failed to initialize clipboard monitor")
    );

    tauri::Builder::default()
        .plugin(tauri_plugin_opener::init())
        .plugin(tauri_plugin_clipboard_manager::init())
        .plugin(tauri_plugin_global_shortcut::Builder::new().build())
        .plugin(tauri_plugin_store::Builder::default().build())
        .manage(AppState {
            clipboard_manager: clipboard_manager.clone(),
            clipboard_monitor: clipboard_monitor.clone(),
        })
        .invoke_handler(tauri::generate_handler![
            get_clipboard_history,
            save_clipboard_entry,
            toggle_favorite,
            delete_clipboard_entry,
            clear_clipboard_history,
            get_preferences,
            save_preferences,
            start_clipboard_monitoring,
            stop_clipboard_monitoring,
            is_monitoring_clipboard,
            get_clipboard_status,
            update_global_shortcuts,
        ])
        .setup(move |app| {
            // Initialize clipboard monitoring
            let _app_handle = app.handle().clone();
            let monitor_clone = clipboard_monitor.clone();

            // Start clipboard monitoring automatically
            tauri::async_runtime::spawn(async move {
                if let Err(e) = monitor_clone.start_monitoring().await {
                    eprintln!("Failed to start clipboard monitoring: {}", e);
                } else {
                    println!("Clipboard monitoring started successfully");
                }
            });

            Ok(())
        })
        .run(tauri::generate_context!())
        .expect("error while running tauri application");
}
